import json
from typing import Dict, Optional
from openai import OpenAI
from ..config import OPENAI_API_KEY, OPENAI_MODEL
from ..tools.lookup_cache import LookupCache


class LLMEntityExtractor:
    """Service for extracting entities using LLM with structured output."""
    
    def __init__(self):
        self.client = OpenAI(api_key=OPENAI_API_KEY)
        self.model = OPENAI_MODEL
    
    def extract_entities(self, user_input: str) -> Dict[str, Optional[str]]:
        """
        Extract sector, country, and continent from user input using LLM.
        
        Args:
            user_input: The user's search query
            
        Returns:
            Dict with extracted entities: {"sector": str, "country": str, "continent": str}
        """
        # Get available options for context
        sectors = self._get_available_sectors()
        countries = self._get_available_countries()
        continents = self._get_available_continents()
        
        system_prompt = f"""You are an expert at extracting business search entities from user queries.

Your task is to extract the following entities from user input:
- sector: Business sector/industry
- country: Country name
- continent: Continent name

Available sectors: {', '.join(sectors)}
Available countries: {', '.join(countries)}
Available continents: {', '.join(continents)}

Rules:
1. Only extract entities that are explicitly mentioned or clearly implied in the user input
2. Use exact matches from the available options when possible
3. If a country is mentioned, try to infer the continent
4. If no entity is found for a category, return null
5. Be conservative - only extract what you're confident about

Return your response as a JSON object with keys: sector, country, continent.
Each value should be either a string (exact match from available options) or null."""

        user_prompt = f"Extract entities from this user query: '{user_input}'"
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"},
                temperature=0.1
            )
            
            result = json.loads(response.choices[0].message.content)
            
            # Validate and clean the result
            return {
                "sector": self._validate_sector(result.get("sector")),
                "country": self._validate_country(result.get("country")),
                "continent": self._validate_continent(result.get("continent"))
            }
            
        except Exception as e:
            print(f"Error in LLM entity extraction: {e}")
            # Fallback to empty result
            return {"sector": None, "country": None, "continent": None}
    
    def _get_available_sectors(self) -> list[str]:
        """Get list of available sector names."""
        try:
            sectors = LookupCache.get_sectors()
            return [s["sector_name"] for s in sectors]
        except Exception:
            return ["diversified retailers", "pharmacy", "banks"]  # fallback
    
    def _get_available_countries(self) -> list[str]:
        """Get list of available country names."""
        try:
            countries = LookupCache.get_countries()
            return [c["country_name"] for c in countries]
        except Exception:
            return ["United Kingdom", "United States", "Germany", "France"]  # fallback
    
    def _get_available_continents(self) -> list[str]:
        """Get list of available continent names."""
        try:
            continents = LookupCache.get_continents()
            return [c["continent_name"] for c in continents]
        except Exception:
            return ["Europe", "North America", "Asia", "Africa", "South America", "Oceania"]  # fallback
    
    def _validate_sector(self, sector: Optional[str]) -> Optional[str]:
        """Validate sector against available options."""
        if not sector:
            return None
        
        available_sectors = self._get_available_sectors()
        # Case-insensitive exact match
        for available in available_sectors:
            if sector.lower() == available.lower():
                return available
        return None
    
    def _validate_country(self, country: Optional[str]) -> Optional[str]:
        """Validate country against available options."""
        if not country:
            return None
            
        available_countries = self._get_available_countries()
        # Case-insensitive exact match
        for available in available_countries:
            if country.lower() == available.lower():
                return available
        return None
    
    def _validate_continent(self, continent: Optional[str]) -> Optional[str]:
        """Validate continent against available options."""
        if not continent:
            return None
            
        available_continents = self._get_available_continents()
        # Case-insensitive exact match
        for available in available_continents:
            if continent.lower() == available.lower():
                return available
        return None
