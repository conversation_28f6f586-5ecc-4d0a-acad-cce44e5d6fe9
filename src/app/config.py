import os
from dotenv import load_dotenv
import os

load_dotenv()

API_BASE_URL = os.getenv("API_BASE_URL", "https://dataplatform.synergy-impact.de")
API_TOKEN = os.getenv("API_TOKEN", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

DEFAULT_CONTINENT_ID = 4  # Europe by default for now (change as needed)

# simple static maps for v1 (you can replace with live lookups)
SECTOR_MAP = {
    "diversified retailers": 57,
    "pharmacy": 57,
    "banks": 7,
}
COUNTRY_MAP = {
    "uk": 184,
    "united kingdom": 184,
    "usa": 185,
    "united states": 185,
}
