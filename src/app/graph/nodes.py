from typing import Dict
from ..config import SECTOR_MAP, COUNTRY_MAP, DEFAULT_CONTINENT_ID
from ..services.api_client import DataPlatformClient

# 1) extract very simply (v1: string contains)
def extract_entities(state: Dict):
    text = (state.get("user_input") or "").lower()
    entities: Dict[str, str] = {}

    for sector in SECTOR_MAP.keys():
        if sector in text:
            entities["sector"] = sector
            break

    for country in COUNTRY_MAP.keys():
        if country in text:
            entities["country"] = country
            break

    state["entities"] = entities
    return state

# 2) resolve to ids (no validation in v1)
def resolve_ids(state: Dict):
    entities = state.get("entities", {})
    ids: Dict[str, int] = {}
    if "sector" in entities:
        ids["sector_id"] = SECTOR_MAP[entities["sector"]]
    if "country" in entities:
        ids["country_id"] = COUNTRY_MAP[entities["country"]]
    state["ids"] = ids
    return state

# 3) call API (assume ids exist; we keep it simple)
_client = DataPlatformClient()

def call_api(state: Dict):
    ids = state.get("ids", {})
    results = _client.get_targets_no_buyer(
        continent_id=DEFAULT_CONTINENT_ID,
        country_id=ids.get("country_id"),
        sector_id=ids.get("sector_id"),
        region=None,
        anonymized_companies=False,
    )
    state["results"] = results
    return state

# 4) format output
def format_response(state: Dict):
    results = state.get("results") or []
    if not results:
        state["final_message"] = "I couldn’t find results for that query."
        return state

    # limit to top 10 for readability
    top = results[:10]
    lines = [
        "Here are the top matches:",
        *[f"- {r.get('company_name')} ({r.get('locations_count')} locations)" for r in top],
    ]
    state["final_message"] = "\n".join(lines)
    return state
